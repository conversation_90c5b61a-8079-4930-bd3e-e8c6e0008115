import sys
sys.path.append("..")

import dotenv
dotenv.load_dotenv()

import os
API_KEY = str(os.getenv("API_KEY"))
API_SECRET = str(os.getenv("API_SECRET"))

import ccxt

mexc = ccxt.mexc(
    {
        "apiKey": API_KEY,
        "secret": API_SECRET,
    }
)

import pandas as pd

df = pd.DataFrame(mexc.fetchOHLCV("BTC_USDT", "1m")) # type: ignore


df.columns = ["時間", "開盤", "高", "低", "close", "volume"]

df["時間"] = pd.to_datetime(df["時間"], unit="ms")

df

