import datetime
import logging
import os
import time
from enum import Enum

import dotenv
import schedule

import gen_indicator
from api import MEXC, OrderSide

dotenv.load_dotenv()


class PositionStatus(Enum):
    NO_POSITION = "no_position"
    LONG = "long"
    SHORT = "short"


logger = logging.getLogger("Main")
logger.setLevel(logging.DEBUG)
logger.addHandler(logging.StreamHandler())
logger.addHandler(logging.FileHandler("trading.log", encoding="utf-8"))

# 交易帳戶資訊
wins = 0
losses = 0
profit = 0
position_status = PositionStatus.NO_POSITION
size = 0
# 止盈止損價位
take_profit_price = 0
stop_loss_price = 0
testnet = True

# 設定止盈和止損點數 (這裡使用絕對點數，可以根據需要調整)
TP_POINTS = 200  # 止盈點數
SL_POINTS = 1200  # 止損點數

API_KEY = str(os.getenv("API_KEY"))
API_SECRET = str(os.getenv("API_SECRET"))
COOKIE = str(os.getenv("COOKIE"))

mexc = MEXC(API_KEY, API_SECRET, COOKIE, testnet)


def job():
    global wins, losses, profit, position_status, take_profit_price, stop_loss_price, size

    current_timestamp = time.time()

    df = gen_indicator.bbd(mexc.get_candles())
    current_close = df.iloc[-1]["close"]

    # --- 1. 平倉邏輯 ---
    # 只有當有部位時才檢查平倉條件
    if position_status == PositionStatus.LONG:
        if current_close >= take_profit_price:
            logger.info("作多止盈平倉！")
            wins += 1
            profit += TP_POINTS
            position_status = PositionStatus.NO_POSITION
            mexc.create_order("BTC_USDT", OrderSide.CLOSE_LONG, size)
        elif current_close <= stop_loss_price:
            logger.info("作多止損平倉！")
            losses += 1
            profit -= SL_POINTS
            position_status = PositionStatus.NO_POSITION
            mexc.create_order("BTC_USDT", OrderSide.CLOSE_LONG, size)
        logger.info(f"loss:{losses}\nwins:{wins}")
    elif position_status == PositionStatus.SHORT:
        if current_close <= take_profit_price:
            logger.info("作空止盈平倉！")
            wins += 1
            profit += TP_POINTS
            position_status = PositionStatus.NO_POSITION
            mexc.create_order("BTC_USDT", OrderSide.CLOSE_SHORT, size)
        elif current_close >= stop_loss_price:
            logger.info("作空止損平倉！")
            losses += 1
            profit -= SL_POINTS
            position_status = PositionStatus.NO_POSITION
            mexc.create_order("BTC_USDT", OrderSide.CLOSE_SHORT, size)
        logger.info(f"loss:{losses}\nwins:{wins}")

    # --- 2. 開倉邏輯 ---
    # 只有當沒有部位時才檢查開倉條件
    if position_status == PositionStatus.NO_POSITION:
        long_condition = df.iloc[-1]["close"] < df.iloc[-1]["BBL"] and df.iloc[-2]["close"] < df.iloc[-2]["BBL"]
        short_condition = df.iloc[-1]["close"] > df.iloc[-1]["BBU"] and df.iloc[-2]["close"] > df.iloc[-2]["BBU"]

        if long_condition:
            size = round(40 / current_close, 5)
            mexc.create_order("BTC_USDT", OrderSide.OPEN_LONG, size)
            logger.info("觸發作多條件，準備開倉！")

            take_profit_price = current_close + TP_POINTS
            stop_loss_price = current_close - SL_POINTS
            position_status = PositionStatus.LONG
            logger.info(f"止盈價位: {take_profit_price}，止損價位: {stop_loss_price}")
        elif short_condition:
            size = round(40 / current_close, 5)
            mexc.create_order("BTC_USDT", OrderSide.OPEN_SHORT, size)
            logger.info("觸發作空條件，準備開倉！")

            take_profit_price = current_close - TP_POINTS
            stop_loss_price = current_close + SL_POINTS
            position_status = PositionStatus.SHORT
            logger.info(f"止盈價位: {take_profit_price}，止損價位: {stop_loss_price}")

    # print(f"目前部位狀態: {position_status}，總獲利: {profit}")
    cost_time = time.time() - current_timestamp
    logger.info(
        f"{datetime.datetime.now()}  now :{int(df.iloc[-1]['close'])}"
        + f" BBU: {int(df.iloc[-1]['BBU'])} BBL:{int(df.iloc[-1]['BBL'])}"
    )
    logger.info(f"資料取得耗時: {cost_time} 秒")


def main():
    schedule.every(30).seconds.do(job)

    job()
    while True:
        schedule.run_pending()
        time.sleep(2)


if __name__ == "__main__":
    main()
