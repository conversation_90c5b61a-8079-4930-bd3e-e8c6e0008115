from enum import Enum

import ccxt
import numpy as np
import pandas as pd
from curl_cffi import requests

from api.signature import mexc_crypto


class OrderSide(Enum):
    OPEN_LONG = 1
    CLOSE_SHORT = 2
    OPEN_SHORT = 3
    CLOSE_LONG = 4


class MEXC:
    def __init__(self, api_key, api_secret, cookie, testnet=True):
        self.api_key = api_key
        self.api_secret = api_secret
        self.cookie = cookie
        self.url: str = (
            "https://futures.testnet.mexc.com/api/v1/private/order/create"
            if testnet
            else "https://futures.mexc.com/api/v1/private/order/create"
        )

        self.mexc = ccxt.mexc(
            {
                "apiKey": self.api_key,
                "secret": self.api_secret,
            }
        )

    def __sign_and_send_order(self, obj: dict):
        signature = mexc_crypto(self.cookie, obj)
        headers = {
            "Content-Type": "application/json",
            "x-mxc-sign": signature["sign"],
            "x-mxc-nonce": signature["time"],
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36",  # noqa: E501
            "Authorization": self.cookie,
        }
        response = requests.post(self.url, headers=headers, json=obj)
        return response.json()

    def create_order(self, symbol: str, side: OrderSide, quantity: float):
        obj = {
            "symbol": symbol,
            "side": side.value,
            "openType": 1,
            "type": "1",
            "vol": quantity,
            "leverage": 1,
            "price": 2.5,
            "priceProtect": "0",
        }

        response = self.__sign_and_send_order(obj)
        return response

    def get_candles(self):
        try:
            klines_data = self.mexc.fetch_ohlcv("BTC_USDT", "1m")
        except Exception as e:
            print(f"Error fetching data: {e}")
            raise e

        df = pd.DataFrame(klines_data)
        df.columns = ["time", "open", "high", "low", "close", "volume"]
        df["time"] = pd.to_datetime(df["time"], unit="ms")

        df_converted = df.astype(
            {
                "open": np.float64,
                "high": np.float64,
                "low": np.float64,
                "close": np.float64,
                "volume": np.float64,
            }
        )

        return df_converted
